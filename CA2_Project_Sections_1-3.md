# System Development Techniques - CA2 Group Assignment
## Sections 1-3: Company Selection, Requirements & Use Cases

---

## Section 1: Choosing a Company

### Selected Company: **Toast Box**

**Industry:** Food & Beverage (F&B)  
**Company Background:** Toast Box is a prominent Singapore-based F&B chain specializing in traditional Asian breakfast and beverages. Founded in 2005, it has grown to over 60 outlets across Singapore and internationally.

**Current Business Model:**
- Traditional dine-in and takeaway service
- Basic POS systems for order processing
- Manual inventory management
- Limited digital customer engagement
- Traditional marketing approaches

### Business Challenges Identified

1. **Operational Inefficiencies**
   - Manual inventory tracking leading to food waste
   - Inconsistent food preparation times
   - Limited staff productivity optimization

2. **Customer Experience Gaps**
   - Long waiting times during peak hours
   - Limited personalization in service
   - Lack of digital ordering options

3. **Business Intelligence Limitations**
   - Limited data analytics on customer preferences
   - Difficulty in demand forecasting
   - Manual reporting processes

### Generative AI Adoption Opportunities

**1. AI-Powered Kitchen Operations**
- Recipe optimization and consistency
- Automated cooking instructions
- Inventory prediction and management

**2. Customer Service Enhancement**
- AI chatbots for ordering and customer support
- Personalized menu recommendations
- Dynamic pricing optimization

**3. Business Intelligence & Analytics**
- Predictive analytics for demand forecasting
- Customer behavior analysis
- Automated reporting and insights

---

## System Vision Document

### Problem Description

Toast Box faces significant challenges in maintaining operational efficiency while scaling their business across multiple locations. The current manual processes for inventory management, order processing, and customer service create bottlenecks that impact both customer satisfaction and profitability. 

Key problems include:
- **Food Waste:** 15-20% of ingredients expire due to poor inventory forecasting
- **Inconsistent Quality:** Manual recipe preparation leads to variations across outlets
- **Long Wait Times:** Average customer wait time of 12-15 minutes during peak hours
- **Limited Customer Insights:** Lack of data-driven understanding of customer preferences
- **Staff Inefficiency:** Manual processes consume 30% more time than necessary

### System Capabilities

The proposed AI-Enhanced Restaurant Management System (AERMS) will provide:

**1. Intelligent Kitchen Management**
- AI-powered recipe generation and optimization
- Real-time cooking guidance with computer vision
- Automated inventory tracking and ordering
- Predictive maintenance for kitchen equipment

**2. Smart Customer Experience Platform**
- AI chatbot for order taking and customer service
- Personalized menu recommendations based on customer history
- Dynamic queue management and wait time prediction
- Mobile app integration for seamless ordering

**3. Business Intelligence Dashboard**
- Real-time analytics on sales, inventory, and customer behavior
- Predictive forecasting for demand planning
- Automated reporting and performance insights
- Multi-outlet performance comparison

**4. Staff Optimization Tools**
- AI-powered staff scheduling based on predicted demand
- Training modules with AI-generated content
- Performance tracking and improvement suggestions

### Business Benefits

**Operational Benefits:**
- **Reduce Food Waste by 60%:** Through accurate demand forecasting and inventory optimization
- **Improve Order Accuracy by 95%:** AI-guided preparation reduces human error
- **Decrease Wait Times by 40%:** Optimized kitchen workflows and predictive ordering
- **Increase Staff Productivity by 35%:** Automated processes and intelligent scheduling

**Financial Benefits:**
- **Cost Savings:** $50,000 annually per outlet through reduced waste and improved efficiency
- **Revenue Growth:** 20% increase through better customer experience and upselling
- **ROI:** Expected 300% return on investment within 18 months

**Strategic Benefits:**
- **Scalability:** Standardized AI-driven processes enable rapid expansion
- **Competitive Advantage:** First-mover advantage in AI-powered F&B operations
- **Data-Driven Decisions:** Real-time insights enable agile business strategies
- **Customer Loyalty:** Personalized experiences increase customer retention by 25%

**Technology Benefits:**
- **Integration:** Seamless connection with existing POS and accounting systems
- **Scalability:** Cloud-based architecture supports business growth
- **Security:** Enterprise-grade data protection and compliance
- **Maintenance:** AI-powered system monitoring and predictive maintenance

---

## Section 2: Identifying System Requirements

### Functional Requirements

**FR1: Order Management**
- System shall process customer orders through multiple channels (mobile app, kiosk, staff terminal)
- System shall integrate with existing POS systems
- System shall support order modifications and cancellations
- System shall generate order confirmations and receipts

**FR2: AI-Powered Menu Recommendations**
- System shall analyze customer purchase history to provide personalized recommendations
- System shall suggest complementary items during order process
- System shall adapt recommendations based on time of day and weather
- System shall support promotional campaigns and special offers

**FR3: Intelligent Inventory Management**
- System shall track ingredient levels in real-time
- System shall predict demand based on historical data and external factors
- System shall automatically generate purchase orders when stock levels are low
- System shall alert staff about expiring ingredients

**FR4: Kitchen Operations Optimization**
- System shall provide AI-generated cooking instructions and recipes
- System shall optimize kitchen workflow based on current orders
- System shall monitor cooking times and quality standards
- System shall coordinate multiple orders for efficient preparation

**FR5: Customer Service Chatbot**
- System shall handle customer inquiries about menu, orders, and store information
- System shall process simple orders and reservations
- System shall escalate complex issues to human staff
- System shall support multiple languages (English, Mandarin, Malay)

**FR6: Business Analytics and Reporting**
- System shall generate real-time dashboards for sales, inventory, and performance metrics
- System shall provide predictive analytics for demand forecasting
- System shall create automated reports for management review
- System shall compare performance across multiple outlets

### Non-Functional Requirements

**NFR1: Performance**
- System response time shall not exceed 2 seconds for customer-facing operations
- System shall support up to 500 concurrent users per outlet
- Database queries shall complete within 1 second for reporting functions

**NFR2: Reliability**
- System uptime shall be 99.9% during business hours
- System shall have automated backup and recovery procedures
- System shall continue basic operations during network outages

**NFR3: Security**
- System shall encrypt all customer data and payment information
- System shall comply with PDPA (Personal Data Protection Act) requirements
- System shall implement role-based access control for staff functions

**NFR4: Usability**
- System interface shall be intuitive for staff with minimal training
- Customer-facing interfaces shall be accessible to users of all technical levels
- System shall support touch-screen interactions for kiosks and tablets

**NFR5: Scalability**
- System shall support addition of new outlets without performance degradation
- System architecture shall handle 10x current transaction volume
- System shall integrate with third-party delivery platforms

### User Stories

**US1:** As a customer, I want to receive personalized menu recommendations so that I can discover new items I might enjoy.

**US2:** As a kitchen staff member, I want AI-generated cooking instructions so that I can prepare consistent, high-quality food efficiently.

**US3:** As a store manager, I want real-time inventory alerts so that I can prevent stockouts and reduce food waste.

**US4:** As a customer, I want to place orders through a mobile app so that I can skip the queue and save time.

**US5:** As a franchise owner, I want predictive analytics dashboards so that I can make data-driven decisions about staffing and inventory.

**US6:** As a customer service staff, I want an AI chatbot to handle routine inquiries so that I can focus on complex customer needs.

**US7:** As a customer, I want accurate wait time estimates so that I can plan my visit accordingly.

**US8:** As a regional manager, I want to compare performance across outlets so that I can identify best practices and areas for improvement.

**US9:** As a kitchen manager, I want automated inventory ordering so that I can maintain optimal stock levels without manual tracking.

**US10:** As a customer, I want to customize my order preferences so that the system remembers my dietary restrictions and preferences for future visits.

**US11:** As a staff member, I want AI-powered scheduling recommendations so that we have optimal staffing during peak and off-peak hours.

**US12:** As a customer, I want to receive order status updates so that I know when my food is ready for pickup or delivery.

---

## Section 3: Developing Use Case

### Use Case Diagram

```
                    AI-Enhanced Restaurant Management System (AERMS)

    Customer                                                    Staff Member
        |                                                           |
        |                                                           |
    [Place Order] ────────────────────────────────────────── [Process Order]
        |                                                           |
    [View Menu] ──────────────────────────────────────────── [Manage Inventory]
        |                                                           |
    [Get Recommendations] ─────────────────────────────────── [View Analytics]
        |                                                           |
    [Check Order Status] ──────────────────────────────────── [Update Menu]
        |                                                           |
    [Make Payment] ────────────────────────────────────────── [Generate Reports]
        |                                                           |
    [Provide Feedback]                                              |
        |                                                           |
        |                    AI System                              |
        |                       |                                   |
        └─────── [Generate Recommendations] ──────────────────────┘
                                |
                    [Predict Demand] ──────────── Kitchen Manager
                                |                       |
                    [Optimize Kitchen Workflow] ────── [Monitor Kitchen Operations]
                                |                       |
                    [Manage Inventory Automatically] ── [Schedule Staff]
                                |                       |
                    [Analyze Customer Behavior] ─────── [Review Performance]
                                                        |
                                                   Store Manager
                                                        |
                                                   [View Business Intelligence]
                                                        |
                                                   [Configure System Settings]
```

### Primary Actors:
- **Customer:** End users who place orders and interact with the system
- **Staff Member:** Front-of-house employees who process orders and serve customers
- **Kitchen Manager:** Manages kitchen operations and food preparation
- **Store Manager:** Oversees overall store operations and business performance
- **AI System:** Automated system that provides intelligent recommendations and optimizations

### Use Case Description Tables

#### Use Case 1: Place Order
| **Use Case ID** | UC001 |
|-----------------|-------|
| **Use Case Name** | Place Order |
| **Actor** | Customer |
| **Description** | Customer places a food order through mobile app, kiosk, or with staff assistance |
| **Preconditions** | Customer has access to menu and payment method |
| **Postconditions** | Order is recorded in system and sent to kitchen for preparation |
| **Main Flow** | 1. Customer selects items from menu<br>2. System calculates total price<br>3. Customer confirms order details<br>4. Customer makes payment<br>5. System generates order confirmation<br>6. Order is sent to kitchen queue |
| **Alternative Flow** | 2a. Customer applies discount code<br>3a. Customer modifies order before confirmation<br>4a. Payment fails - customer retries or cancels |
| **Exceptions** | - System unavailable<br>- Payment processing error<br>- Item out of stock |

#### Use Case 2: Generate AI Recommendations
| **Use Case ID** | UC002 |
|-----------------|-------|
| **Use Case Name** | Generate AI Recommendations |
| **Actor** | AI System, Customer |
| **Description** | AI system analyzes customer data and provides personalized menu recommendations |
| **Preconditions** | Customer profile exists or order history available |
| **Postconditions** | Personalized recommendations displayed to customer |
| **Main Flow** | 1. AI system analyzes customer purchase history<br>2. System considers time of day, weather, and preferences<br>3. AI generates ranked list of recommendations<br>4. Recommendations displayed to customer<br>5. Customer can accept or ignore suggestions |
| **Alternative Flow** | 1a. New customer - system uses general popularity data<br>4a. Customer provides feedback on recommendations |
| **Exceptions** | - AI service unavailable<br>- Insufficient data for recommendations |

#### Use Case 3: Manage Inventory
| **Use Case ID** | UC003 |
|-----------------|-------|
| **Use Case Name** | Manage Inventory |
| **Actor** | Kitchen Manager, AI System |
| **Description** | Track ingredient levels and automatically manage inventory replenishment |
| **Preconditions** | Inventory tracking system is operational |
| **Postconditions** | Inventory levels updated and purchase orders generated if needed |
| **Main Flow** | 1. System monitors ingredient usage in real-time<br>2. AI predicts future demand based on historical data<br>3. System alerts when stock levels are low<br>4. Automatic purchase orders generated for approved suppliers<br>5. Manager reviews and approves orders |
| **Alternative Flow** | 3a. Manager manually adjusts stock levels<br>4a. Manager overrides automatic ordering |
| **Exceptions** | - Supplier unavailable<br>- Budget constraints<br>- System tracking error |

#### Use Case 4: Process Kitchen Operations
| **Use Case ID** | UC004 |
|-----------------|-------|
| **Use Case Name** | Process Kitchen Operations |
| **Actor** | Kitchen Staff, AI System |
| **Description** | AI optimizes kitchen workflow and provides cooking guidance |
| **Preconditions** | Orders are in kitchen queue and ingredients are available |
| **Postconditions** | Food is prepared according to standards and orders are completed |
| **Main Flow** | 1. AI analyzes current orders and kitchen capacity<br>2. System optimizes preparation sequence<br>3. AI provides step-by-step cooking instructions<br>4. Staff follows AI guidance for food preparation<br>5. System tracks completion times and quality |
| **Alternative Flow** | 3a. Staff overrides AI instructions based on experience<br>4a. Special dietary requirements require manual adjustment |
| **Exceptions** | - Equipment malfunction<br>- Ingredient shortage<br>- Staff unavailable |

#### Use Case 5: View Business Analytics
| **Use Case ID** | UC005 |
|-----------------|-------|
| **Use Case Name** | View Business Analytics |
| **Actor** | Store Manager |
| **Description** | Manager accesses real-time business intelligence and performance reports |
| **Preconditions** | Manager has appropriate access permissions |
| **Postconditions** | Manager has current business insights for decision making |
| **Main Flow** | 1. Manager logs into analytics dashboard<br>2. System displays real-time performance metrics<br>3. Manager selects specific reports or time periods<br>4. AI generates insights and recommendations<br>5. Manager exports reports or takes action based on insights |
| **Alternative Flow** | 3a. Manager customizes dashboard views<br>4a. Manager sets up automated alerts |
| **Exceptions** | - Data synchronization issues<br>- Report generation failure<br>- Access permission denied |

#### Use Case 6: Customer Service Chatbot
| **Use Case ID** | UC006 |
|-----------------|-------|
| **Use Case Name** | Customer Service Chatbot |
| **Actor** | Customer, AI System |
| **Description** | AI chatbot handles customer inquiries and provides automated support |
| **Preconditions** | Customer has access to chat interface |
| **Postconditions** | Customer inquiry is resolved or escalated to human staff |
| **Main Flow** | 1. Customer initiates chat session<br>2. AI chatbot greets customer and identifies inquiry type<br>3. Chatbot provides relevant information or assistance<br>4. Customer confirms if issue is resolved<br>5. Chat session ends with satisfaction survey |
| **Alternative Flow** | 3a. Complex issue requires human intervention<br>4a. Customer requests to speak with staff member |
| **Exceptions** | - AI service unavailable<br>- Language not supported<br>- Technical issue prevents chat |

#### Use Case 7: Predict Demand
| **Use Case ID** | UC007 |
|-----------------|-------|
| **Use Case Name** | Predict Demand |
| **Actor** | AI System |
| **Description** | AI analyzes historical data and external factors to forecast demand |
| **Preconditions** | Historical sales data and external data sources available |
| **Postconditions** | Demand predictions generated for inventory and staffing planning |
| **Main Flow** | 1. AI collects historical sales data<br>2. System incorporates external factors (weather, events, holidays)<br>3. AI applies machine learning models to predict demand<br>4. System generates forecasts for different time periods<br>5. Predictions are used for automated planning |
| **Alternative Flow** | 2a. External data unavailable - use historical patterns only<br>4a. Manager adjusts predictions based on local knowledge |
| **Exceptions** | - Insufficient historical data<br>- Model prediction failure<br>- External data source error |

#### Use Case 8: Monitor Kitchen Operations
| **Use Case ID** | UC008 |
|-----------------|-------|
| **Use Case Name** | Monitor Kitchen Operations |
| **Actor** | Kitchen Manager |
| **Description** | Manager oversees kitchen performance and ensures quality standards |
| **Preconditions** | Kitchen operations are active with orders in progress |
| **Postconditions** | Kitchen performance is monitored and any issues are addressed |
| **Main Flow** | 1. Manager accesses kitchen monitoring dashboard<br>2. System displays real-time kitchen metrics<br>3. Manager reviews order completion times and quality scores<br>4. Manager identifies bottlenecks or issues<br>5. Manager takes corrective action if needed |
| **Alternative Flow** | 4a. System automatically alerts manager of critical issues<br>5a. Manager adjusts staffing or workflow |
| **Exceptions** | - Monitoring system failure<br>- Critical equipment breakdown<br>- Staff shortage |

#### Use Case 9: Schedule Staff
| **Use Case ID** | UC009 |
|-----------------|-------|
| **Use Case Name** | Schedule Staff |
| **Actor** | Store Manager, AI System |
| **Description** | AI assists in creating optimal staff schedules based on predicted demand |
| **Preconditions** | Staff availability and demand predictions are available |
| **Postconditions** | Optimized staff schedule is created and communicated |
| **Main Flow** | 1. AI analyzes predicted demand for upcoming period<br>2. System considers staff availability and skills<br>3. AI generates optimal schedule recommendations<br>4. Manager reviews and adjusts schedule as needed<br>5. Schedule is published and staff are notified |
| **Alternative Flow** | 3a. Manager manually overrides AI recommendations<br>4a. Staff request schedule changes |
| **Exceptions** | - Insufficient staff available<br>- Last-minute staff unavailability<br>- System scheduling conflict |

#### Use Case 10: Configure System Settings
| **Use Case ID** | UC010 |
|-----------------|-------|
| **Use Case Name** | Configure System Settings |
| **Actor** | Store Manager |
| **Description** | Manager configures system parameters and business rules |
| **Preconditions** | Manager has administrative access to system |
| **Postconditions** | System operates according to updated configuration |
| **Main Flow** | 1. Manager accesses system administration panel<br>2. Manager selects configuration category<br>3. Manager updates settings and parameters<br>4. System validates configuration changes<br>5. Changes are applied and system is updated |
| **Alternative Flow** | 4a. Validation fails - manager corrects invalid settings<br>5a. Changes require system restart |
| **Exceptions** | - Invalid configuration values<br>- System update failure<br>- Access permission denied |

---

## Summary

This document covers the first three sections of the CA2 project:

1. **Company Selection:** Toast Box was chosen as a representative Singapore F&B company with clear opportunities for AI adoption
2. **System Vision:** Comprehensive analysis of problems, capabilities, and benefits of implementing an AI-Enhanced Restaurant Management System
3. **Requirements Analysis:** Detailed functional and non-functional requirements with 12 user stories
4. **Use Case Development:** Complete use case diagram and 10 detailed use case descriptions covering all major system interactions

The proposed system addresses real business challenges in the F&B industry while leveraging Generative AI to improve operations, customer experience, and business intelligence. The solution is scalable and aligns with Singapore's digital transformation initiatives.
