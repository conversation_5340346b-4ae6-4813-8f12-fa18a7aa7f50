# System Development Techniques - CA2 Group Assignment

**Course:** Diploma in Information Technology  
**Module:** System Development Techniques  
**Semester:** July 2025  
**Due Date:** 11 August 2025 (Monday), 11:59 AM  

## Assessment Overview

- **Total Marks:** 100 marks
- **Weight:** 40% of overall assessment
  - Group assignment: 30%
  - Group presentation: 10%

## Project Rationale

The group project enables collaborative learning and teamwork skills essential in the workplace. Students must apply theoretical concepts to real-world scenarios.

**Important:** Students are not permitted to:
- Reuse old assignments
- Submit projects from previous semesters
- Copy content from sources, particularly the Internet

## Group Formation

- **Group Size:** 4-5 students per group
- Each member must contribute fairly
- Groups manage their own dynamics and timely completion
- Resolve issues early through lecturer mediation
- Last-minute mediation will not be entertained
- Peer evaluation available as final resort
- Lecturer may assign different marks to individuals who don't contribute fairly

## Case Study

The Infocomm Media Development Authority of Singapore (IMDA) is launching two initiatives:
- **Generative AI (GenAI) Playbook**
- **GenAI Navigator**

These initiatives aim to make artificial intelligence (AI) more accessible to Singaporean businesses and increase local adoption.

These initiatives reflect Singapore's commitment to:
- Advancing AI adoption
- Supporting small and medium-sized enterprises (SMEs)
- Educating the public
- Equipping the workforce with necessary skills for the digital economy

By fostering innovation and continuous learning, Singapore positions itself as a global leader in digital transformation, ensuring people and businesses are well-prepared for future challenges and opportunities.

### Reference
Singapore: Infocomm Media Development Authority unveils GenAI initiatives to propel AI adoption and digital transformation – Baker McKenzie  
[https://connectontech.bakermckenzie.com/singapore-infocomm-media-development-authority-unveils-genai-initiatives-to-propel-ai-adoption-and-digital-transformation-2/](https://connectontech.bakermckenzie.com/singapore-infocomm-media-development-authority-unveils-genai-initiatives-to-propel-ai-adoption-and-digital-transformation-2/)

## Group Task

Your group is tasked with assisting a Singapore company in adopting Generative AI for their business. The company may already have a digital system or may currently operate without one.

### Objectives
- Explore how the company could introduce a revitalized suite of digital solutions
- Integrate Generative AI into products and services
- Address the company's specific challenges
- Document how Generative AI adaptation will help their business
- Apply methodologies and processes from the Systems Development Techniques module

## Research Report Requirements

Your group will produce a comprehensive research report, with a maximum length of **6000 words**, which may include relevant illustrations, diagrams, or prototypes. The report should cover the following points:

### 1. Choosing a Company
- Select a Singapore company for your research
- The industry must be one of the following:
  - Food & Beverage (F&B)
  - Online E-commerce
  - Hotel / Accommodation
  - Gaming
- Explore how the company can adopt Generative AI to enhance digital connectivity and strengthen digital capabilities
- Conduct interviews with company representatives (if feasible) to understand specific business needs
- Develop a **System Vision Document** that captures the company's vision for digital transformation

### 2. Identifying System Requirements
- Define system requirements for the new digital solution based on interviews and the System Vision Document
- Specify key activities the system must support and constraints it must meet
- Outline both functional and non-functional requirements
- Create at least **ten (10) user stories** based on functional requirements

### 3. Developing Use Case
Create the following diagrams and documents based on the User Stories:
- Use Case Diagram
- Use Case Description Table for each identified use case

### 4. Designing the System
Illustrate the system's design with the following diagrams and documents:
- Design Class Diagram
- Sequence Diagram (Interaction Diagram)
- Package Diagram (derived from the elements in the Design Class Diagram)

### 5. System Development Life Cycle (SDLC) Approach & Model
- Describe the SDLC approach and model chosen for developing the system
- Explain why it was selected

### 6. User Acceptance Testing (UAT)
- Describe the plan for conducting User Acceptance Testing
- Include key stages and methods

### 7. Presentation Slides
- Prepare presentation slides that clearly summarize points 1 to 6
- Will be used for the final project presentation

**Note:** The presentation slides will be used for the actual presentation.

### Important Guidelines
- Ensure each section of the report aligns with the case study provided
- Explanations and illustrations should be as detailed as possible
- Include any relevant diagrams or prototypes that enhance clarity and understanding

## Assessment Marks Allocation

| Component | Marks |
|-----------|-------|
| Point 1 (System Vision Document) | 15 |
| Point 2 (Requirements & User Stories) | 15 |
| Point 3 (Use Case) | 25 |
| Point 4 (UML Documents) | 20 |
| Point 5 (SDLC Approach) | 10 |
| Point 6 (UAT) | 10 |
| Point 7 (Group Presentation Slides) | 5 |
| **Total** | **100** |

## Assessment Topics
Topics 1 to 16

## Report Format Requirements

### Report Structure
1. The report should include:
   - Cover page
   - Table of contents
   - Introduction
   - Task write-up
   - Conclusion
   - References
   - Appendices

2. **Cover page must include:**
   - Institution name (and institution logo) and programme title
   - Module name, semester and year
   - Date of submission
   - All group members' full name and student ID

3. Each question should be a separate section
4. References should be presented in **Harvard format** with at least **THREE (3) references**
5. Students should keep a copy of assignment submitted

### Formatting Requirements
- **Word Limit:** Not exceeding 6000 words
- **Font:** Arial or Calibri, black colored
- **Font Size:** 12 with 1½ or double spacing

## Important Information

### Penalty Marks for Late Submission
- **Late by one day:** 20% deducted from total marks
- **More than one day:** submission will NOT be graded

### Important Dates
- **CA2 Group Assignment Deadline:** 11 August 2025 (Monday), 11:59 AM
- Submit your assignment via Canvas
- All assignment files must be submitted to be graded

### Lecturer Contact
Contact your lecturer via your SIM email for any project issues.

### Plagiarism and Collusion
- The submitted report must demonstrate that it is the student's own work
- No marks will be awarded if there are no workings or reasonable explanations
- Plagiarism and collusion are serious offenses
- All cases will be referred to administration
- Grades will be withheld if submission is suspected of plagiarism or collusion until investigations are completed

### Submitting Assignment

**File Naming Convention:** Group_No_SIM_SDT_CA2

**Files to submit:**
1. Research Report (Word document in .docx format)
2. Presentation Slides (PowerPoint presentation in .pptx format)

**Example for Group 2:**
1. `Group_2_SIM_SDT_CA2.docx`
2. `Group_2_SIM_SDT_CA2.pptx`

**Note:** Students are advised to retain a copy of the submitted assignment for their records.

## Appendix - Assessment Rubric

The following assessment criteria will be used to evaluate your research report (100 marks total):

### Assessment Criteria Summary

**1. System Vision Document (15 marks)**
- Include all required elements: Problem Description, System Capabilities, and Business Benefits
- Well-analyzed and accurately presented
- Showcase main functions of the envisioned system

**2. Requirements & User Stories (15 marks)**
- Include all 10 use cases
- Both use case diagram and descriptions must be present
- Accurately represent main system functions with appropriate actors
- Clear and easy to understand with minimal errors

**3. Use Case Diagram (25 marks)**
- Complete use case diagrams and descriptions
- Accurate representation of system functionality
- Clear logical flow with minimal diagramming errors

**4. UML Documents (20 marks)**
- Design Class Diagram using correct UML format
- All necessary classes present with appropriate detail
- Correct relationships, cardinality, and inheritance
- Package Diagram with proper structure
- Sequence Diagram depicting scenarios correctly

**5. SDLC Approach (10 marks)**
- Detailed explanation of chosen SDLC technique
- Justification for selection with advantages and disadvantages
- Supporting examples

**6. User Acceptance Testing (10 marks)**
- Detailed UAT explanation
- Clear justification of how UAT relates to solution development
- May include supporting illustrations or diagrams

**7. Group Presentation Slides (5 marks)**
- Clear summary of all points 1-6
- Professional presentation format

### Grading Scale
Each component will be assessed on a scale of:
- **Excellent:** Comprehensive, accurate, and well-presented
- **Very Good:** Complete with minor issues
- **Good:** Mostly complete with some missing elements
- **Acceptable:** Basic requirements met
- **Weak:** Incomplete or poorly executed

---

**End of Requirements Document**
