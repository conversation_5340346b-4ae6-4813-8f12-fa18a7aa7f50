# CA2 Project - Mermaid Diagrams
## AI-Enhanced Restaurant Management System for Toast Box

---

## Use Case Diagram

```mermaid
graph TB
    %% Actors
    Customer[👤 Customer]
    FrontStaff[👥 Front-of-House Staff]
    KitchenStaff[👨‍🍳 Kitchen Staff]
    KitchenManager[👨‍💼 Kitchen Manager]
    StoreManager[👩‍💼 Store Manager]
    RegionalManager[🏢 Regional Manager]
    AISystem[🤖 AI System]
    
    %% System Boundary
    subgraph AERMS["AI-Enhanced Restaurant Management System (AERMS)"]
        %% Customer Use Cases
        BrowseMenu[Browse Menu with AI Recommendations]
        PlaceOrder[Place Order with AI Assistance]
        TrackOrder[Track Order Status]
        MakePayment[Make Payment]
        ProvideFeedback[Provide Feedback]
        
        %% Staff Use Cases
        ProcessOrders[Process Orders]
        HandlePayments[Handle Payments]
        ManageCustomerService[Manage Customer Service]
        UpdateMenu[Update Menu Items]
        
        %% Kitchen Use Cases
        FollowInstructions[Follow AI Cooking Instructions]
        MonitorQuality[Monitor Food Quality]
        ReportIssues[Report Equipment Issues]
        
        %% Kitchen Manager Use Cases
        ScheduleKitchen[Schedule Kitchen Operations]
        ManageInventory[Manage Inventory]
        
        %% Store Manager Use Cases
        ConfigureSystem[Configure System Settings]
        ReviewMetrics[Review Performance Metrics]
        
        %% Regional Manager Use Cases
        CompareOutlets[Compare Outlet Performance]
        StrategicPlanning[Strategic Planning]
        
        %% AI System Use Cases
        GenerateRecommendations[Generate Personalized Recommendations]
        OptimizeWorkflow[Optimize Kitchen Workflow]
        PredictInventory[Predict Inventory Needs]
        AnalyzeBehavior[Analyze Customer Behavior]
        GenerateInsights[Generate Business Insights]
        ForecastDemand[Forecast Demand]
        MonitorPerformance[Monitor System Performance]
        CreateReports[Create Analytics Reports]
        OptimizePricing[Optimize Pricing]
        BenchmarkPerformance[Benchmark Performance]
    end
    
    %% Customer Connections
    Customer --> BrowseMenu
    Customer --> PlaceOrder
    Customer --> TrackOrder
    Customer --> MakePayment
    Customer --> ProvideFeedback
    
    %% Front Staff Connections
    FrontStaff --> ProcessOrders
    FrontStaff --> HandlePayments
    FrontStaff --> ManageCustomerService
    FrontStaff --> UpdateMenu
    
    %% Kitchen Staff Connections
    KitchenStaff --> FollowInstructions
    KitchenStaff --> MonitorQuality
    KitchenStaff --> ReportIssues
    
    %% Kitchen Manager Connections
    KitchenManager --> ScheduleKitchen
    KitchenManager --> ManageInventory
    
    %% Store Manager Connections
    StoreManager --> ConfigureSystem
    StoreManager --> ReviewMetrics
    
    %% Regional Manager Connections
    RegionalManager --> CompareOutlets
    RegionalManager --> StrategicPlanning
    
    %% AI System Connections
    AISystem --> GenerateRecommendations
    AISystem --> OptimizeWorkflow
    AISystem --> PredictInventory
    AISystem --> AnalyzeBehavior
    AISystem --> GenerateInsights
    AISystem --> ForecastDemand
    AISystem --> MonitorPerformance
    AISystem --> CreateReports
    AISystem --> OptimizePricing
    AISystem --> BenchmarkPerformance
    
    %% Include/Extend Relationships
    BrowseMenu -.->|includes| GenerateRecommendations
    PlaceOrder -.->|includes| MakePayment
    OptimizeWorkflow -.->|includes| MonitorQuality
    ManageInventory -.->|includes| PredictInventory
    ReviewMetrics -.->|includes| CreateReports
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    classDef ai fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class Customer,FrontStaff,KitchenStaff,KitchenManager,StoreManager,RegionalManager actor
    class BrowseMenu,PlaceOrder,TrackOrder,MakePayment,ProvideFeedback,ProcessOrders,HandlePayments,ManageCustomerService,UpdateMenu,FollowInstructions,MonitorQuality,ReportIssues,ScheduleKitchen,ManageInventory,ConfigureSystem,ReviewMetrics,CompareOutlets,StrategicPlanning usecase
    class AISystem,GenerateRecommendations,OptimizeWorkflow,PredictInventory,AnalyzeBehavior,GenerateInsights,ForecastDemand,MonitorPerformance,CreateReports,OptimizePricing,BenchmarkPerformance ai
```

---

## System Architecture Overview

```mermaid
graph TD
    %% User Interfaces
    subgraph UI["User Interfaces"]
        MobileApp[📱 Mobile App]
        WebPortal[🌐 Web Portal]
        Kiosk[🖥️ In-Store Kiosk]
        StaffTerminal[💻 Staff Terminal]
    end
    
    %% API Gateway
    APIGateway[🚪 API Gateway]
    
    %% Core Services
    subgraph CoreServices["Core AI Services"]
        OrderService[📋 Order Management Service]
        RecommendationEngine[🎯 AI Recommendation Engine]
        InventoryService[📦 Inventory Management Service]
        KitchenService[👨‍🍳 Kitchen Operations Service]
        ChatbotService[🤖 AI Chatbot Service]
        AnalyticsService[📊 Business Analytics Service]
    end
    
    %% AI/ML Layer
    subgraph AILayer["AI/ML Processing Layer"]
        NLP[🗣️ Natural Language Processing]
        MLModels[🧠 Machine Learning Models]
        ComputerVision[👁️ Computer Vision]
        PredictiveAnalytics[📈 Predictive Analytics]
    end
    
    %% Data Layer
    subgraph DataLayer["Data Management Layer"]
        CustomerDB[(👥 Customer Database)]
        InventoryDB[(📦 Inventory Database)]
        OrderDB[(📋 Order Database)]
        AnalyticsDB[(📊 Analytics Database)]
    end
    
    %% External Systems
    subgraph External["External Systems"]
        PaymentGateway[💳 Payment Gateway]
        SupplierAPI[🚚 Supplier APIs]
        WeatherAPI[🌤️ Weather API]
        POS[🏪 Existing POS Systems]
    end
    
    %% Connections
    UI --> APIGateway
    APIGateway --> CoreServices
    CoreServices --> AILayer
    CoreServices --> DataLayer
    CoreServices --> External
    
    %% Styling
    classDef ui fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef core fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ai fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class MobileApp,WebPortal,Kiosk,StaffTerminal ui
    class OrderService,RecommendationEngine,InventoryService,KitchenService,ChatbotService,AnalyticsService core
    class NLP,MLModels,ComputerVision,PredictiveAnalytics ai
    class CustomerDB,InventoryDB,OrderDB,AnalyticsDB data
    class PaymentGateway,SupplierAPI,WeatherAPI,POS external
```

---

## AI Recommendation Flow

```mermaid
flowchart TD
    Start([Customer Opens Menu]) --> CheckProfile{Customer Profile Exists?}
    
    CheckProfile -->|Yes| LoadHistory[Load Purchase History]
    CheckProfile -->|No| UseGeneral[Use General Popularity Data]
    
    LoadHistory --> AnalyzeContext[Analyze Context:<br/>• Time of day<br/>• Weather<br/>• Special events<br/>• Dietary preferences]
    UseGeneral --> AnalyzeContext
    
    AnalyzeContext --> MLProcessing[🧠 ML Model Processing:<br/>• Collaborative filtering<br/>• Content-based filtering<br/>• Hybrid recommendations]
    
    MLProcessing --> GenerateRecs[Generate Ranked Recommendations]
    
    GenerateRecs --> ApplyFilters{Apply Business Rules}
    ApplyFilters --> FilterStock[Filter Out-of-Stock Items]
    FilterStock --> FilterDietary[Apply Dietary Restrictions]
    FilterDietary --> FilterPromotions[Prioritize Promotional Items]
    
    FilterPromotions --> DisplayRecs[Display Personalized Recommendations]
    
    DisplayRecs --> UserInteraction{User Interaction}
    UserInteraction -->|Clicks Item| UpdateBehavior[Update Behavior Data]
    UserInteraction -->|Adds to Cart| TrackSelection[Track Selection]
    UserInteraction -->|Ignores| RecordFeedback[Record Implicit Feedback]
    
    UpdateBehavior --> RefineLive[Refine Recommendations in Real-time]
    TrackSelection --> RefineLive
    RecordFeedback --> RefineLive
    
    RefineLive --> DisplayRecs
    
    UserInteraction -->|Places Order| End([Order Placed])
    
    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef end fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    
    class Start,End start
    class LoadHistory,UseGeneral,AnalyzeContext,GenerateRecs,FilterStock,FilterDietary,FilterPromotions,DisplayRecs,UpdateBehavior,TrackSelection,RecordFeedback,RefineLive process
    class CheckProfile,ApplyFilters,UserInteraction decision
    class MLProcessing ai
```

---

## Kitchen Workflow Optimization

```mermaid
flowchart TD
    OrderReceived([New Order Received]) --> AnalyzeOrder[🧠 AI Analyzes Order:<br/>• Item complexity<br/>• Preparation time<br/>• Required equipment<br/>• Staff skills needed]
    
    AnalyzeOrder --> CheckCapacity{Check Kitchen Capacity}
    
    CheckCapacity -->|Available| OptimizeSequence[🎯 Optimize Preparation Sequence:<br/>• Minimize wait times<br/>• Balance workload<br/>• Coordinate equipment usage]
    CheckCapacity -->|Busy| QueueOrder[Add to Priority Queue]
    
    QueueOrder --> WaitForCapacity[Wait for Kitchen Capacity]
    WaitForCapacity --> OptimizeSequence
    
    OptimizeSequence --> AssignTasks[👨‍🍳 Assign Tasks to Staff:<br/>• Match skills to requirements<br/>• Balance workload<br/>• Consider current assignments]
    
    AssignTasks --> GenerateInstructions[📋 Generate AI Cooking Instructions:<br/>• Step-by-step guidance<br/>• Timing specifications<br/>• Quality checkpoints<br/>• Visual references]
    
    GenerateInstructions --> StartPreparation[🔥 Start Food Preparation]
    
    StartPreparation --> MonitorProgress{👁️ Monitor Progress}
    
    MonitorProgress -->|On Track| ContinuePrep[Continue Preparation]
    MonitorProgress -->|Delayed| AdjustWorkflow[⚡ Adjust Workflow:<br/>• Reassign tasks<br/>• Modify sequence<br/>• Alert management]
    MonitorProgress -->|Quality Issue| QualityCheck[🔍 Quality Intervention:<br/>• Stop preparation<br/>• Provide guidance<br/>• Restart if needed]
    
    ContinuePrep --> QualityGate{Final Quality Check}
    AdjustWorkflow --> MonitorProgress
    QualityCheck --> MonitorProgress
    
    QualityGate -->|Pass| CompleteOrder[✅ Order Complete]
    QualityGate -->|Fail| QualityCorrection[🔧 Quality Correction]
    
    QualityCorrection --> MonitorProgress
    
    CompleteOrder --> NotifyCustomer[📱 Notify Customer]
    CompleteOrder --> UpdateMetrics[📊 Update Performance Metrics]
    
    NotifyCustomer --> End([Order Ready])
    UpdateMetrics --> End
    
    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef quality fill:#ffebee,stroke:#e91e63,stroke-width:2px
    classDef end fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    
    class OrderReceived,End start
    class CheckCapacity,MonitorProgress,QualityGate decision
    class AnalyzeOrder,OptimizeSequence,AssignTasks,GenerateInstructions ai
    class QualityCheck,QualityCorrection quality
    class QueueOrder,WaitForCapacity,StartPreparation,ContinuePrep,AdjustWorkflow,CompleteOrder,NotifyCustomer,UpdateMetrics process
```
