# CA2 Project - Mermaid Diagrams
## AI-Enhanced Restaurant Management System for Toast Box

---

## Use Case Diagram

```mermaid
graph TB
    %% Actors
    Customer[Customer]
    FrontStaff[Front-of-House Staff]
    KitchenStaff[Kitchen Staff]
    KitchenManager[Kitchen Manager]
    StoreManager[Store Manager]
    RegionalManager[Regional Manager]
    AISystem[AI System]
    
    %% System Boundary
    subgraph AERMS["AI-Enhanced Restaurant Management System (AERMS)"]
        %% Customer Use Cases
        BrowseMenu[Browse Menu with AI Recommendations]
        PlaceOrder[Place Order with AI Assistance]
        TrackOrder[Track Order Status]
        MakePayment[Make Payment]
        ProvideFeedback[Provide Feedback]
        
        %% Staff Use Cases
        ProcessOrders[Process Orders]
        HandlePayments[Handle Payments]
        ManageCustomerService[Manage Customer Service]
        UpdateMenu[Update Menu Items]
        
        %% Kitchen Use Cases
        FollowInstructions[Follow AI Cooking Instructions]
        MonitorQuality[Monitor Food Quality]
        ReportIssues[Report Equipment Issues]
        
        %% Kitchen Manager Use Cases
        ScheduleKitchen[Schedule Kitchen Operations]
        ManageInventory[Manage Inventory]
        
        %% Store Manager Use Cases
        ConfigureSystem[Configure System Settings]
        ReviewMetrics[Review Performance Metrics]
        
        %% Regional Manager Use Cases
        CompareOutlets[Compare Outlet Performance]
        StrategicPlanning[Strategic Planning]
        
        %% AI System Use Cases
        GenerateRecommendations[Generate Personalized Recommendations]
        OptimizeWorkflow[Optimize Kitchen Workflow]
        PredictInventory[Predict Inventory Needs]
        AnalyzeBehavior[Analyze Customer Behavior]
        GenerateInsights[Generate Business Insights]
        ForecastDemand[Forecast Demand]
        MonitorPerformance[Monitor System Performance]
        CreateReports[Create Analytics Reports]
        OptimizePricing[Optimize Pricing]
        BenchmarkPerformance[Benchmark Performance]
    end
    
    %% Customer Connections
    Customer --> BrowseMenu
    Customer --> PlaceOrder
    Customer --> TrackOrder
    Customer --> MakePayment
    Customer --> ProvideFeedback
    
    %% Front Staff Connections
    FrontStaff --> ProcessOrders
    FrontStaff --> HandlePayments
    FrontStaff --> ManageCustomerService
    FrontStaff --> UpdateMenu
    
    %% Kitchen Staff Connections
    KitchenStaff --> FollowInstructions
    KitchenStaff --> MonitorQuality
    KitchenStaff --> ReportIssues
    
    %% Kitchen Manager Connections
    KitchenManager --> ScheduleKitchen
    KitchenManager --> ManageInventory
    
    %% Store Manager Connections
    StoreManager --> ConfigureSystem
    StoreManager --> ReviewMetrics
    
    %% Regional Manager Connections
    RegionalManager --> CompareOutlets
    RegionalManager --> StrategicPlanning
    
    %% AI System Connections
    AISystem --> GenerateRecommendations
    AISystem --> OptimizeWorkflow
    AISystem --> PredictInventory
    AISystem --> AnalyzeBehavior
    AISystem --> GenerateInsights
    AISystem --> ForecastDemand
    AISystem --> MonitorPerformance
    AISystem --> CreateReports
    AISystem --> OptimizePricing
    AISystem --> BenchmarkPerformance
    
    %% Include/Extend Relationships
    BrowseMenu -.->|includes| GenerateRecommendations
    PlaceOrder -.->|includes| MakePayment
    OptimizeWorkflow -.->|includes| MonitorQuality
    ManageInventory -.->|includes| PredictInventory
    ReviewMetrics -.->|includes| CreateReports
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    classDef ai fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class Customer,FrontStaff,KitchenStaff,KitchenManager,StoreManager,RegionalManager actor
    class BrowseMenu,PlaceOrder,TrackOrder,MakePayment,ProvideFeedback,ProcessOrders,HandlePayments,ManageCustomerService,UpdateMenu,FollowInstructions,MonitorQuality,ReportIssues,ScheduleKitchen,ManageInventory,ConfigureSystem,ReviewMetrics,CompareOutlets,StrategicPlanning usecase
    class AISystem,GenerateRecommendations,OptimizeWorkflow,PredictInventory,AnalyzeBehavior,GenerateInsights,ForecastDemand,MonitorPerformance,CreateReports,OptimizePricing,BenchmarkPerformance ai
```

---

## System Architecture Overview

```mermaid
graph TD
    %% User Interfaces
    subgraph UI["User Interfaces"]
        MobileApp[Mobile App]
        WebPortal[Web Portal]
        Kiosk[In-Store Kiosk]
        StaffTerminal[Staff Terminal]
    end

    %% API Gateway
    APIGateway[API Gateway]
    
    %% Core Services
    subgraph CoreServices["Core AI Services"]
        OrderService[Order Management Service]
        RecommendationEngine[AI Recommendation Engine]
        InventoryService[Inventory Management Service]
        KitchenService[Kitchen Operations Service]
        ChatbotService[AI Chatbot Service]
        AnalyticsService[Business Analytics Service]
    end

    %% AI/ML Layer
    subgraph AILayer["AI/ML Processing Layer"]
        NLP[Natural Language Processing]
        MLModels[Machine Learning Models]
        ComputerVision[Computer Vision]
        PredictiveAnalytics[Predictive Analytics]
    end

    %% Data Layer
    subgraph DataLayer["Data Management Layer"]
        CustomerDB[(Customer Database)]
        InventoryDB[(Inventory Database)]
        OrderDB[(Order Database)]
        AnalyticsDB[(Analytics Database)]
    end

    %% External Systems
    subgraph External["External Systems"]
        PaymentGateway[Payment Gateway]
        SupplierAPI[Supplier APIs]
        WeatherAPI[Weather API]
        POS[Existing POS Systems]
    end
    
    %% Connections
    UI --> APIGateway
    APIGateway --> CoreServices
    CoreServices --> AILayer
    CoreServices --> DataLayer
    CoreServices --> External
    
    %% Styling
    classDef ui fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef core fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ai fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class MobileApp,WebPortal,Kiosk,StaffTerminal ui
    class OrderService,RecommendationEngine,InventoryService,KitchenService,ChatbotService,AnalyticsService core
    class NLP,MLModels,ComputerVision,PredictiveAnalytics ai
    class CustomerDB,InventoryDB,OrderDB,AnalyticsDB data
    class PaymentGateway,SupplierAPI,WeatherAPI,POS external
```

---

## AI Recommendation Flow

```mermaid
flowchart TD
    Start([Customer Opens Menu]) --> CheckProfile{Customer Profile Exists?}
    
    CheckProfile -->|Yes| LoadHistory[Load Purchase History]
    CheckProfile -->|No| UseGeneral[Use General Popularity Data]
    
    LoadHistory --> AnalyzeContext[Analyze Context:<br/>• Time of day<br/>• Weather<br/>• Special events<br/>• Dietary preferences]
    UseGeneral --> AnalyzeContext
    
    AnalyzeContext --> MLProcessing[🧠 ML Model Processing:<br/>• Collaborative filtering<br/>• Content-based filtering<br/>• Hybrid recommendations]
    
    MLProcessing --> GenerateRecs[Generate Ranked Recommendations]
    
    GenerateRecs --> ApplyFilters{Apply Business Rules}
    ApplyFilters --> FilterStock[Filter Out-of-Stock Items]
    FilterStock --> FilterDietary[Apply Dietary Restrictions]
    FilterDietary --> FilterPromotions[Prioritize Promotional Items]
    
    FilterPromotions --> DisplayRecs[Display Personalized Recommendations]
    
    DisplayRecs --> UserInteraction{User Interaction}
    UserInteraction -->|Clicks Item| UpdateBehavior[Update Behavior Data]
    UserInteraction -->|Adds to Cart| TrackSelection[Track Selection]
    UserInteraction -->|Ignores| RecordFeedback[Record Implicit Feedback]
    
    UpdateBehavior --> RefineLive[Refine Recommendations in Real-time]
    TrackSelection --> RefineLive
    RecordFeedback --> RefineLive
    
    RefineLive --> DisplayRecs
    
    UserInteraction -->|Places Order| End([Order Placed])
    
    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef end fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    
    class Start,End start
    class LoadHistory,UseGeneral,AnalyzeContext,GenerateRecs,FilterStock,FilterDietary,FilterPromotions,DisplayRecs,UpdateBehavior,TrackSelection,RecordFeedback,RefineLive process
    class CheckProfile,ApplyFilters,UserInteraction decision
    class MLProcessing ai
```

---

## Kitchen Workflow Optimization

```mermaid
flowchart TD
    OrderReceived([New Order Received]) --> AnalyzeOrder[🧠 AI Analyzes Order:<br/>• Item complexity<br/>• Preparation time<br/>• Required equipment<br/>• Staff skills needed]
    
    AnalyzeOrder --> CheckCapacity{Check Kitchen Capacity}
    
    CheckCapacity -->|Available| OptimizeSequence[🎯 Optimize Preparation Sequence:<br/>• Minimize wait times<br/>• Balance workload<br/>• Coordinate equipment usage]
    CheckCapacity -->|Busy| QueueOrder[Add to Priority Queue]
    
    QueueOrder --> WaitForCapacity[Wait for Kitchen Capacity]
    WaitForCapacity --> OptimizeSequence
    
    OptimizeSequence --> AssignTasks[👨‍🍳 Assign Tasks to Staff:<br/>• Match skills to requirements<br/>• Balance workload<br/>• Consider current assignments]
    
    AssignTasks --> GenerateInstructions[📋 Generate AI Cooking Instructions:<br/>• Step-by-step guidance<br/>• Timing specifications<br/>• Quality checkpoints<br/>• Visual references]
    
    GenerateInstructions --> StartPreparation[🔥 Start Food Preparation]
    
    StartPreparation --> MonitorProgress{👁️ Monitor Progress}
    
    MonitorProgress -->|On Track| ContinuePrep[Continue Preparation]
    MonitorProgress -->|Delayed| AdjustWorkflow[⚡ Adjust Workflow:<br/>• Reassign tasks<br/>• Modify sequence<br/>• Alert management]
    MonitorProgress -->|Quality Issue| QualityCheck[🔍 Quality Intervention:<br/>• Stop preparation<br/>• Provide guidance<br/>• Restart if needed]
    
    ContinuePrep --> QualityGate{Final Quality Check}
    AdjustWorkflow --> MonitorProgress
    QualityCheck --> MonitorProgress
    
    QualityGate -->|Pass| CompleteOrder[✅ Order Complete]
    QualityGate -->|Fail| QualityCorrection[🔧 Quality Correction]
    
    QualityCorrection --> MonitorProgress
    
    CompleteOrder --> NotifyCustomer[📱 Notify Customer]
    CompleteOrder --> UpdateMetrics[📊 Update Performance Metrics]
    
    NotifyCustomer --> End([Order Ready])
    UpdateMetrics --> End
    
    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef quality fill:#ffebee,stroke:#e91e63,stroke-width:2px
    classDef end fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    
    class OrderReceived,End start
    class CheckCapacity,MonitorProgress,QualityGate decision
    class AnalyzeOrder,OptimizeSequence,AssignTasks,GenerateInstructions ai
    class QualityCheck,QualityCorrection quality
    class QueueOrder,WaitForCapacity,StartPreparation,ContinuePrep,AdjustWorkflow,CompleteOrder,NotifyCustomer,UpdateMetrics process
```

---

## Inventory Prediction and Management

```mermaid
flowchart TD
    Start([Daily Inventory Analysis]) --> CollectData[📊 Collect Historical Data:<br/>• Sales patterns<br/>• Seasonal trends<br/>• Consumption rates<br/>• Waste patterns]

    CollectData --> ExternalFactors[🌍 Gather External Factors:<br/>• Weather forecast<br/>• Local events<br/>• Holidays<br/>• Market trends]

    ExternalFactors --> MLAnalysis[🧠 ML Predictive Analysis:<br/>• Time series forecasting<br/>• Regression models<br/>• Pattern recognition<br/>• Anomaly detection]

    MLAnalysis --> GenerateForecast[📈 Generate Demand Forecast:<br/>• Next 7 days<br/>• Item-level predictions<br/>• Confidence intervals<br/>• Risk assessments]

    GenerateForecast --> CurrentStock{Check Current Stock Levels}

    CurrentStock -->|Sufficient| MonitorConsumption[📱 Monitor Real-time Consumption]
    CurrentStock -->|Low Stock| GenerateAlert[🚨 Generate Low Stock Alert]
    CurrentStock -->|Critical| UrgentOrder[⚡ Urgent Reorder Required]

    GenerateAlert --> CalculateOrder[🧮 Calculate Optimal Order Quantity:<br/>• Lead times<br/>• Storage capacity<br/>• Budget constraints<br/>• Supplier minimums]

    UrgentOrder --> EmergencyProcurement[📞 Emergency Procurement Process]

    CalculateOrder --> SelectSupplier[🏪 Select Optimal Supplier:<br/>• Price comparison<br/>• Quality ratings<br/>• Delivery speed<br/>• Reliability score]

    SelectSupplier --> AutoOrder{Auto-order Enabled?}

    AutoOrder -->|Yes| PlaceOrder[📋 Place Automatic Order]
    AutoOrder -->|No| ManagerApproval[👨‍💼 Request Manager Approval]

    ManagerApproval -->|Approved| PlaceOrder
    ManagerApproval -->|Rejected| AdjustOrder[🔧 Adjust Order Parameters]

    AdjustOrder --> SelectSupplier

    PlaceOrder --> TrackDelivery[🚚 Track Delivery Status]
    EmergencyProcurement --> TrackDelivery

    TrackDelivery --> ReceiveGoods[📦 Receive and Verify Goods]

    ReceiveGoods --> UpdateInventory[📊 Update Inventory System]
    MonitorConsumption --> UpdateInventory

    UpdateInventory --> QualityCheck{Quality Inspection}

    QualityCheck -->|Pass| StockItems[✅ Stock Items]
    QualityCheck -->|Fail| ReturnGoods[❌ Return/Replace Goods]

    ReturnGoods --> SelectSupplier

    StockItems --> UpdateMetrics[📈 Update Performance Metrics:<br/>• Forecast accuracy<br/>• Supplier performance<br/>• Waste reduction<br/>• Cost optimization]

    UpdateMetrics --> End([Inventory Optimized])

    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef alert fill:#ffebee,stroke:#e91e63,stroke-width:2px
    classDef end fill:#e8f5e8,stroke:#4caf50,stroke-width:2px

    class Start,End start
    class CurrentStock,AutoOrder,QualityCheck decision
    class MLAnalysis,GenerateForecast ai
    class GenerateAlert,UrgentOrder,EmergencyProcurement alert
    class CollectData,ExternalFactors,MonitorConsumption,CalculateOrder,SelectSupplier,PlaceOrder,ManagerApproval,AdjustOrder,TrackDelivery,ReceiveGoods,UpdateInventory,ReturnGoods,StockItems,UpdateMetrics process
```

---

## Customer Service Chatbot Flow

```mermaid
flowchart TD
    CustomerStart([Customer Initiates Chat]) --> Greeting[🤖 AI Greeting:<br/>Hello! How can I help you today?]

    Greeting --> IdentifyIntent[🧠 Analyze Customer Message:<br/>• Natural Language Processing<br/>• Intent classification<br/>• Entity extraction<br/>• Sentiment analysis]

    IdentifyIntent --> IntentType{Identify Intent Type}

    IntentType -->|Menu Inquiry| MenuHelp[📋 Provide Menu Information:<br/>• Item descriptions<br/>• Nutritional info<br/>• Allergen warnings<br/>• Pricing]

    IntentType -->|Order Status| OrderStatus[📱 Check Order Status:<br/>• Retrieve order details<br/>• Current preparation stage<br/>• Estimated completion time<br/>• Delivery tracking]

    IntentType -->|Place Order| OrderAssist[🛒 Order Assistance:<br/>• Guide through menu<br/>• Add items to cart<br/>• Apply promotions<br/>• Process payment]

    IntentType -->|Complaint| ComplaintHandle[😔 Handle Complaint:<br/>• Acknowledge concern<br/>• Gather details<br/>• Offer solutions<br/>• Escalate if needed]

    IntentType -->|General Info| GeneralInfo[ℹ️ Provide General Information:<br/>• Store locations<br/>• Operating hours<br/>• Contact details<br/>• Policies]

    IntentType -->|Complex Issue| EscalateHuman[👨‍💼 Escalate to Human Agent]

    MenuHelp --> FollowUp{Customer Satisfied?}
    OrderStatus --> FollowUp
    OrderAssist --> ProcessOrder[💳 Process Order Through Chat]
    ComplaintHandle --> ResolutionCheck{Issue Resolved?}
    GeneralInfo --> FollowUp

    ProcessOrder --> FollowUp

    ResolutionCheck -->|Yes| FollowUp
    ResolutionCheck -->|No| EscalateHuman

    EscalateHuman --> HumanTakeover[👥 Human Agent Takes Over:<br/>• Transfer conversation history<br/>• Provide context<br/>• Continue assistance]

    HumanTakeover --> HumanResolution[🤝 Human Resolution Process]

    FollowUp -->|Yes| AdditionalHelp{Need Additional Help?}
    FollowUp -->|No| Feedback[📝 Collect Feedback:<br/>• Service rating<br/>• Suggestions<br/>• Experience quality]

    AdditionalHelp -->|Yes| IntentType
    AdditionalHelp -->|No| Feedback

    Feedback --> ThankYou[🙏 Thank Customer:<br/>Thank you for choosing Toast Box!<br/>Have a great day!]

    HumanResolution --> ThankYou

    ThankYou --> LogInteraction[📊 Log Interaction Data:<br/>• Conversation summary<br/>• Resolution outcome<br/>• Performance metrics<br/>• Learning points]

    LogInteraction --> UpdateKnowledge[🧠 Update AI Knowledge Base:<br/>• New FAQ entries<br/>• Improved responses<br/>• Pattern recognition<br/>• Model training data]

    UpdateKnowledge --> End([Chat Session Complete])

    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef human fill:#ffebee,stroke:#e91e63,stroke-width:2px
    classDef end fill:#e8f5e8,stroke:#4caf50,stroke-width:2px

    class CustomerStart,End start
    class IntentType,FollowUp,ResolutionCheck,AdditionalHelp decision
    class Greeting,IdentifyIntent,UpdateKnowledge ai
    class EscalateHuman,HumanTakeover,HumanResolution human
    class MenuHelp,OrderStatus,OrderAssist,ComplaintHandle,GeneralInfo,ProcessOrder,Feedback,ThankYou,LogInteraction process
```

---

## Business Analytics Dashboard Flow

```mermaid
flowchart TD
    ManagerLogin([Manager Logs In]) --> AuthCheck{Authentication Successful?}

    AuthCheck -->|No| LoginError[❌ Login Failed]
    AuthCheck -->|Yes| LoadDashboard[📊 Load Main Dashboard]

    LoginError --> ManagerLogin

    LoadDashboard --> DataCollection[📈 Real-time Data Collection:<br/>• Sales data<br/>• Inventory levels<br/>• Customer metrics<br/>• Staff performance<br/>• Equipment status]

    DataCollection --> AIAnalysis[🧠 AI Data Analysis:<br/>• Trend identification<br/>• Pattern recognition<br/>• Anomaly detection<br/>• Predictive modeling<br/>• Comparative analysis]

    AIAnalysis --> GenerateInsights[💡 Generate Business Insights:<br/>• Performance summaries<br/>• Recommendations<br/>• Risk alerts<br/>• Opportunities<br/>• Action items]

    GenerateInsights --> DisplayDashboard[🖥️ Display Interactive Dashboard:<br/>• KPI widgets<br/>• Charts and graphs<br/>• Real-time metrics<br/>• Alert notifications<br/>• Quick actions]

    DisplayDashboard --> UserAction{Manager Action}

    UserAction -->|View Details| DrillDown[🔍 Drill-down Analysis:<br/>• Detailed reports<br/>• Historical comparisons<br/>• Segment analysis<br/>• Root cause analysis]

    UserAction -->|Generate Report| ReportType{Select Report Type}

    UserAction -->|Set Alerts| ConfigureAlerts[⚠️ Configure Alert Settings:<br/>• Threshold values<br/>• Notification preferences<br/>• Escalation rules<br/>• Frequency settings]

    UserAction -->|Compare Outlets| OutletComparison[🏪 Multi-outlet Comparison:<br/>• Performance benchmarking<br/>• Best practice identification<br/>• Resource allocation<br/>• Standardization opportunities]

    ReportType -->|Sales Report| SalesReport[💰 Generate Sales Report]
    ReportType -->|Inventory Report| InventoryReport[📦 Generate Inventory Report]
    ReportType -->|Staff Report| StaffReport[👥 Generate Staff Performance Report]
    ReportType -->|Customer Report| CustomerReport[😊 Generate Customer Analytics Report]
    ReportType -->|Financial Report| FinancialReport[💼 Generate Financial Report]

    SalesReport --> ExportOptions[📤 Export Options:<br/>• PDF format<br/>• Excel spreadsheet<br/>• Email delivery<br/>• Scheduled reports]
    InventoryReport --> ExportOptions
    StaffReport --> ExportOptions
    CustomerReport --> ExportOptions
    FinancialReport --> ExportOptions

    DrillDown --> ActionPlan{Create Action Plan?}
    ConfigureAlerts --> SaveSettings[💾 Save Alert Settings]
    OutletComparison --> BestPractices[⭐ Identify Best Practices]

    ActionPlan -->|Yes| CreateTasks[📋 Create Action Items:<br/>• Task assignments<br/>• Deadlines<br/>• Success metrics<br/>• Follow-up schedules]
    ActionPlan -->|No| BackToDashboard[↩️ Return to Dashboard]

    CreateTasks --> NotifyTeam[📧 Notify Team Members]
    SaveSettings --> BackToDashboard
    BestPractices --> ShareInsights[📢 Share Insights Across Outlets]
    ExportOptions --> BackToDashboard

    NotifyTeam --> TrackProgress[📊 Track Action Item Progress]
    ShareInsights --> BackToDashboard

    TrackProgress --> BackToDashboard
    BackToDashboard --> DisplayDashboard

    UserAction -->|Logout| SessionEnd[🚪 End Session]
    SessionEnd --> LogActivity[📝 Log User Activity:<br/>• Session duration<br/>• Actions performed<br/>• Reports generated<br/>• Insights accessed]

    LogActivity --> End([Session Complete])

    %% Styling
    classDef start fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e1f5fe,stroke:#03a9f4,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef ai fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef report fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef end fill:#ffcdd2,stroke:#f44336,stroke-width:2px

    class ManagerLogin,End start
    class AuthCheck,UserAction,ReportType,ActionPlan decision
    class AIAnalysis,GenerateInsights ai
    class SalesReport,InventoryReport,StaffReport,CustomerReport,FinancialReport report
    class LoadDashboard,LoginError,DataCollection,DisplayDashboard,DrillDown,ConfigureAlerts,OutletComparison,ExportOptions,SaveSettings,BestPractices,CreateTasks,BackToDashboard,NotifyTeam,ShareInsights,TrackProgress,SessionEnd,LogActivity process
```

---

## Summary

This file contains all the Mermaid diagrams for the CA2 project sections 1-3:

1. **Use Case Diagram** - Shows all actors and their interactions with the AERMS system
2. **System Architecture Overview** - High-level system components and their relationships
3. **AI Recommendation Flow** - Detailed process for generating personalized recommendations
4. **Kitchen Workflow Optimization** - AI-driven kitchen operations management
5. **Inventory Prediction and Management** - Predictive inventory system workflow
6. **Customer Service Chatbot Flow** - AI chatbot interaction process
7. **Business Analytics Dashboard Flow** - Manager dashboard and reporting system

Each diagram uses proper Mermaid syntax and can be rendered in any Mermaid-compatible viewer or documentation system.
